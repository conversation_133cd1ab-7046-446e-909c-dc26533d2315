"""
Tests for ConversationMessageRepository update functionality.
"""

import json
from uuid import uuid4

import pytest

from constants.message import MessageRole, MessageType
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository
from schemas import MessageValidator


# Mock classes to avoid durable functions import issues in tests
class MockEngagementPeriod:
    def __init__(self, start_date=None, end_date=None):
        self.start_date = start_date
        self.end_date = end_date


class MockFinalExtractionDataResults:
    def __init__(
        self, client_names=None, lead_member_countries=None, periods=None, objective_and_scope=None, outcomes=None
    ):
        self.client_names = client_names
        self.lead_member_countries = lead_member_countries
        self.periods = periods
        self.objective_and_scope = objective_and_scope
        self.outcomes = outcomes


class TestConversationMessageRepositoryUpdate:
    """Test the update functionality of ConversationMessageRepository."""

    async def test_update_content_success(
        self,
        db_session,
        test_conversation_id,
    ):
        """Test that message content can be updated successfully."""
        # Arrange
        conversation_repo = ConversationRepository(db_session)
        message_repo = ConversationMessageRepository(db_session, conversation_repo)

        # Create a test message
        original_content = 'Original message content'
        message_data = MessageValidator(
            conversation_id=test_conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=original_content,
        )

        created_message = await message_repo.create(message_data)
        await db_session.commit()

        # Act - Update the message content
        new_content = '<p>Updated message content with <strong>HTML</strong></p>'
        extracted_options = [
            {'type': 'client_name', 'client_name': 'Test Client'},
        ]
        options = json.dumps(extracted_options)
        await message_repo.update_fields(created_message.id, {'Content': new_content, 'Options': options})
        await db_session.commit()

        # Assert - Verify the content was updated
        updated_message = await message_repo.get(created_message.id)
        assert updated_message.content == new_content
        assert updated_message.id == created_message.id
        assert updated_message.conversation_id == test_conversation_id
        assert updated_message.options[0].client_name == extracted_options[0]['client_name']

    async def test_update_content_nonexistent_message(
        self,
        db_session,
    ):
        """Test that updating a nonexistent message raises EntityNotFoundError."""
        # Arrange
        conversation_repo = ConversationRepository(db_session)
        message_repo = ConversationMessageRepository(db_session, conversation_repo)

        nonexistent_id = uuid4()
        new_content = 'This should fail'

        # Act & Assert
        with pytest.raises(EntityNotFoundError) as exc_info:
            await message_repo.update_fields(nonexistent_id, {'Content': new_content})

        assert 'Message' in str(exc_info.value)
        assert str(nonexistent_id) in str(exc_info.value)

    async def test_update_content_preserves_other_fields(
        self,
        db_session,
        test_conversation_id,
    ):
        """Test that updating content preserves other message fields."""
        # Arrange
        conversation_repo = ConversationRepository(db_session)
        message_repo = ConversationMessageRepository(db_session, conversation_repo)

        # Create a test message with specific properties
        original_content = 'Original content'
        message_data = MessageValidator(
            conversation_id=test_conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=original_content,
        )

        created_message = await message_repo.create(message_data)
        await db_session.commit()

        # Store original values
        original_id = created_message.id
        original_role = created_message.role
        original_type = created_message.type
        original_created_at = created_message.created_at

        # Act - Update only the content
        new_content = 'Updated content'
        await message_repo.update_fields(created_message.id, {'Content': new_content})
        await db_session.commit()

        # Assert - Verify only content changed
        updated_message = await message_repo.get(created_message.id)
        assert updated_message.content == new_content
        assert updated_message.id == original_id
        assert updated_message.role == original_role
        assert updated_message.type == original_type
        assert updated_message.created_at == original_created_at
        assert updated_message.conversation_id == test_conversation_id


class TestExtractedDataMessageFormatting:
    """Test the extracted data message formatting functionality."""

    def _format_extracted_data_message_test(self, extraction_data) -> str:
        """
        Test version of the format function to avoid durable functions import issues.
        This mirrors the actual implementation in document_processing.py
        """
        message_parts = ["<p>Great! I've successfully extracted the following information from your document:</p>"]

        # Client Names
        if extraction_data.client_names:
            client_names_str = ', '.join(extraction_data.client_names)
            message_parts.append(f'<p><strong>Client Name(s):</strong> {client_names_str}</p>')

        # Lead Member Firm Countries
        if extraction_data.lead_member_countries:
            countries_str = ', '.join(extraction_data.lead_member_countries)
            message_parts.append(f'<p><strong>Lead Member Firm Country/Countries:</strong> {countries_str}</p>')

        # Engagement Dates
        if extraction_data.periods:
            period = extraction_data.periods[0]  # Use first period as done in the main function
            if period.start_date or period.end_date:
                date_info = []
                if period.start_date:
                    date_info.append(f'Start: {period.start_date}')
                if period.end_date:
                    date_info.append(f'End: {period.end_date}')
                dates_str = ' | '.join(date_info)
                message_parts.append(f'<p><strong>Engagement Dates:</strong> {dates_str}</p>')

        # Objective and Scope
        if extraction_data.objective_and_scope:
            message_parts.append(f'<p><strong>Objective & Scope:</strong> {extraction_data.objective_and_scope}</p>')

        # Outcomes
        if extraction_data.outcomes:
            message_parts.append(f'<p><strong>Outcomes:</strong> {extraction_data.outcomes}</p>')

        # Add next steps
        message_parts.append(
            "<p>I'll use this information to help create your qual. If you need to make any corrections or add more details, please let me know!</p>"
        )

        return ''.join(message_parts)

    def test_format_extracted_data_message_complete_data(self):
        """Test formatting with complete extracted data."""
        # Arrange
        extraction_data = MockFinalExtractionDataResults(
            client_names=['Microsoft Corporation', 'Apple Inc.'],
            lead_member_countries=['United States', 'Canada'],
            periods=[MockEngagementPeriod(start_date='2024-01-01', end_date='2024-06-30')],
            objective_and_scope='Improve supply chain efficiency and reduce costs',
            outcomes='Achieved 25% cost reduction and 30% efficiency improvement',
        )

        # Act
        result = self._format_extracted_data_message_test(extraction_data)

        # Assert
        assert "<p>Great! I've successfully extracted the following information from your document:</p>" in result
        assert '<strong>Client Name(s):</strong> Microsoft Corporation, Apple Inc.' in result
        assert '<strong>Lead Member Firm Country/Countries:</strong> United States, Canada' in result
        assert '<strong>Engagement Dates:</strong> Start: 2024-01-01 | End: 2024-06-30' in result
        assert '<strong>Objective & Scope:</strong> Improve supply chain efficiency and reduce costs' in result
        assert '<strong>Outcomes:</strong> Achieved 25% cost reduction and 30% efficiency improvement' in result
        assert "I'll use this information to help create your qual" in result

    def test_format_extracted_data_message_partial_data(self):
        """Test formatting with partial extracted data."""
        # Arrange - Only some fields populated
        extraction_data = MockFinalExtractionDataResults(
            client_names=['Test Client'], objective_and_scope='Test objective'
        )

        # Act
        result = self._format_extracted_data_message_test(extraction_data)

        # Assert
        assert '<strong>Client Name(s):</strong> Test Client' in result
        assert '<strong>Objective & Scope:</strong> Test objective' in result
        # Should not contain sections for missing data
        assert 'Lead Member Firm Country' not in result
        assert 'Engagement Dates' not in result
        assert 'Outcomes' not in result

    def test_format_extracted_data_message_empty_data(self):
        """Test formatting with empty extracted data."""
        # Arrange
        extraction_data = MockFinalExtractionDataResults()

        # Act
        result = self._format_extracted_data_message_test(extraction_data)

        # Assert
        assert "<p>Great! I've successfully extracted the following information from your document:</p>" in result
        assert "I'll use this information to help create your qual" in result
        # Should not contain any data sections
        assert 'Client Name' not in result
        assert 'Lead Member Firm' not in result
        assert 'Engagement Dates' not in result
        assert 'Objective & Scope' not in result
        assert 'Outcomes' not in result
