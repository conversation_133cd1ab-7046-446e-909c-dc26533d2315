from datetime import date, datetime
import logging
from typing import Sequence

from constants.message import ALL_REQUIRED_FIELDS_CONFIRMED, NEED_INFO_INITIAL_PROMPT
from schemas import AggregatedData, ClientNameOption, DatePickerOption, LDMFCountryOption, Option


__all__ = ['SystemMessageGenerationService']


logger = logging.getLogger(__name__)


class SystemMessageGenerationService:
    """
    Service for generating system messages based on extracted data.

    This service consolidates the system message generation logic from Azure Durable Functions
    into the main FastAPI application for better maintainability and immediate response generation.
    """

    def format_extracted_data_message(self, aggregated_data: AggregatedData) -> str:
        """
        Format aggregated data into a user-friendly system message content.

        Args:
            aggregated_data: The aggregated data from all sources

        Returns:
            Formatted message content as HTML string
        """
        if aggregated_data.is_complete:
            return ALL_REQUIRED_FIELDS_CONFIRMED

        if aggregated_data.all_fields_none:
            return NEED_INFO_INITIAL_PROMPT

        message_parts = ["<p>Great! I've successfully extracted the following information from your document:</p>"]

        # Client Names
        if aggregated_data.client_name:
            client_names_str = ', '.join(aggregated_data.client_name)
            message_parts.append(f'<p><strong>Client Name(s):</strong> {client_names_str}</p>')

        # Lead Member Firm Countries
        if aggregated_data.ldmf_country:
            countries_str = ', '.join(aggregated_data.ldmf_country)
            message_parts.append(f'<p><strong>Lead Member Firm Country/Countries:</strong> {countries_str}</p>')

        # Engagement Dates
        if aggregated_data.date_intervals:
            # Use first date interval as done in the durable functions
            start_date, end_date = aggregated_data.date_intervals[0]
            if start_date or end_date:
                date_info = []
                if start_date:
                    date_info.append(f'Start: {start_date}')
                if end_date:
                    date_info.append(f'End: {end_date}')
                dates_str = ' | '.join(date_info)
                message_parts.append(f'<p><strong>Engagement Dates:</strong> {dates_str}</p>')

        # Objective and Scope
        if aggregated_data.objective_and_scope:
            message_parts.append(f'<p><strong>Objective & Scope:</strong> {aggregated_data.objective_and_scope}</p>')

        # Outcomes
        if aggregated_data.outcomes:
            message_parts.append(f'<p><strong>Outcomes:</strong> {aggregated_data.outcomes}</p>')

        # Add next steps
        message_parts.append(
            "<p>I'll use this information to help create your qual. If you need to make any corrections or add more details, please let me know!</p>"
        )

        return ''.join(message_parts)

    def generate_options(self, aggregated_data: AggregatedData) -> Sequence[Option]:
        """
        Generate user selection options based on aggregated data.

        Args:
            aggregated_data: The aggregated data from all sources

        Returns:
            List of option dictionaries for user selection
        """
        options = []
        if aggregated_data.is_complete or aggregated_data.all_fields_none:
            return options

        # Client Names - highest priority
        if aggregated_data.client_name and len(aggregated_data.client_name) > 1:
            options.extend([ClientNameOption(client_name=name) for name in aggregated_data.client_name])
            return options

        # Lead Member Firm Countries - second priority
        if aggregated_data.ldmf_country and len(aggregated_data.ldmf_country) > 1:
            options.extend([LDMFCountryOption(ldmf_country=country) for country in aggregated_data.ldmf_country])
            return options

        # Engagement Dates - third priority
        if aggregated_data.date_intervals:
            for start_date_str, end_date_str in aggregated_data.date_intervals:
                if start_date_str or end_date_str:
                    start_date = self._parse_date_string(start_date_str) if start_date_str else None
                    end_date = self._parse_date_string(end_date_str) if end_date_str else None
                    options.append(DatePickerOption(start_date=start_date, end_date=end_date))
                    return options

        return options

    def _parse_date_string(self, date_str: str) -> date | None:
        """
        Parse date string to date object.

        Args:
            date_str: Date string in ISO format (YYYY-MM-DD)

        Returns:
            Date object or None if parsing fails
        """
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            logger.warning('Failed to parse date string "%s": %s', date_str, e)
            return None
