from abc import ABC, abstractmethod
import asyncio
import logging

from constants.extracted_data import Field<PERSON>tat<PERSON>, RequiredField
from constants.message import (
    CLIENT_NAME_MULTIPLE_OPTIONS,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    DATES_CONFIRMATION,
    LDMF_COUNTRY_MULTIPLE_OPTIONS,
    LDMF_COUNTRY_SINGLE_CONFIRMATION,
    NEED_INFO_CLIENT_NAME,
    NEED_INFO_DATES,
    NEED_INFO_LDMF_COUNTRY,
    NEED_INFO_OBJECTIVE_SCOPE,
    NEED_INFO_OUTCOMES,
    OUTCOMES_AGGREGATED_QUESTION,
)
from repositories import LDMFCountriesRepository, QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


logger = logging.getLogger(__name__)


__all__ = [
    '<PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>nt<PERSON><PERSON>and<PERSON>',
    '<PERSON><PERSON><PERSON>val<PERSON>Hand<PERSON>',
    '<PERSON>bject<PERSON>Hand<PERSON>',
    'OutcomesHandler',
    'BaseFieldHandler',
    'TokenRequiredFieldHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass  # pragma: no cover


class TokenRequiredFieldHandler(ABC):
    """
    Abstract base class for handlers that require external API authentication.
    This interface is for handlers that need a token for external API calls.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            token: MSAL token from the user for API authentication

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass  # pragma: no cover


class ClientNameHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    def __init__(self, quals_clients_repository: QualsClientsRepository):
        self.quals_clients_repository = quals_clients_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        # Check if client name is already confirmed
        if confirmed_data.client_name is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        client_names = aggregated_data.client_name
        if client_names:
            if len(client_names) > 1:
                return await self._handle_multiple_client_names(client_names, token)

            if len(client_names) == 1:
                return await self._handle_single_client_name(client_names[0], token)

        # fallback and if client_names is empty
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=NEED_INFO_CLIENT_NAME,
            next_expected_field=None,
        )

    async def _handle_multiple_client_names(self, client_names, token):
        try:
            search_requests = [
                ClientSearchRequest(contains=client_name, page_size=5, page_idx=0) for client_name in client_names
            ]
            search_results = await asyncio.gather(
                *[self.quals_clients_repository.search_clients(request, token) for request in search_requests],
                return_exceptions=True,
            )
            found_client_names = []
            failed_searches = []
            for i, result in enumerate(search_results):
                if isinstance(result, Exception):
                    failed_searches.append((client_names[i], result))
                else:
                    clients = [client.name for client in result.clients]  # type: ignore
                    found_client_names.extend(clients)
            if failed_searches:
                # Log all failed searches for debugging
                for client_name, error in failed_searches:
                    logger.warning(f"Failed to search for client '{client_name}': {error}")
                raise Exception(f'Failed to search for {len(failed_searches)} client(s)')
            if len(found_client_names) == 1:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=found_client_names[0]),
                    next_expected_field=None,
                    options=[],
                )
            else:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.PENDING_CONFIRMATION,
                    system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                    next_expected_field=None,
                    options=found_client_names,
                )
        except Exception:
            # Fallback: show all client names as options
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=client_names,
            )

    async def _handle_single_client_name(self, client_name, token):
        try:
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.quals_clients_repository.search_clients(search_request, token)
            if search_result.clients and len(search_result.clients) == 1:
                return FieldHandlerResponse(
                    needs_confirmation=False,
                    field_status=FieldStatus.CONFIRMED,
                    system_message=None,
                    next_expected_field=None,
                    options=[],
                )
            elif search_result.clients and len(search_result.clients) > 1:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.MULTIPLE,
                    system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                    next_expected_field=None,
                    options=[],
                )
            else:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=CLIENT_NOT_FOUND_PROMPT.format(client_name=client_name),
                    next_expected_field=None,
                    options=[],
                )
        except Exception:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                next_expected_field=None,
                options=[],
            )


class LDMFCountryHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    def __init__(self, ldmf_countries_repository: LDMFCountriesRepository):
        self.ldmf_countries_repository = ldmf_countries_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        if confirmed_data.ldmf_country:
            return self._create_confirmed_response()

        try:
            all_countries_names = await self._fetch_all_ldmf_country_names(token)
        except Exception as e:
            logger.error('Error fetching LDMF countries: %s', e)
            return self._create_missing_ldmf_country_response()

        extracted_countries = aggregated_data.ldmf_country
        if not extracted_countries:
            return self._create_missing_ldmf_country_response()

        if len(extracted_countries) > 1:
            return self._handle_multiple_ldmf_countries(extracted_countries, all_countries_names)
        else:  # len(extracted_countries) == 1
            return self._handle_single_ldmf_country(extracted_countries[0], all_countries_names)

    def _create_confirmed_response(self) -> FieldHandlerResponse:
        """Creates a response for a confirmed field."""
        return FieldHandlerResponse(
            needs_confirmation=False,
            system_message=None,
            next_expected_field=None,
            field_status=FieldStatus.CONFIRMED,
        )

    def _create_missing_ldmf_country_response(self) -> FieldHandlerResponse:
        """Creates a response for a missing LDMF country."""
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=NEED_INFO_LDMF_COUNTRY,
            next_expected_field=None,
        )

    async def _fetch_all_ldmf_country_names(self, token: str) -> set[str]:
        """Fetches all LDMF country names from the repository."""
        all_countries = await self.ldmf_countries_repository.get_ldmf_countries(token)
        return {item.name.lower() for item in all_countries}

    def _handle_multiple_ldmf_countries(
        self, extracted_countries: list[str], all_countries_names: set[str]
    ) -> FieldHandlerResponse:
        """Handles scenarios where multiple LDMF countries are extracted."""
        confirmed_countries = [item for item in extracted_countries if item.lower() in all_countries_names]
        if confirmed_countries:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.PENDING_CONFIRMATION,
                system_message=LDMF_COUNTRY_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=confirmed_countries,
            )
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=LDMF_COUNTRY_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=extracted_countries,
            )

    def _handle_single_ldmf_country(self, ldmf_country: str, all_countries_names: set[str]) -> FieldHandlerResponse:
        """Handles scenarios where a single LDMF country is extracted."""
        if ldmf_country.lower() in all_countries_names:
            return self._create_confirmed_response()
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=LDMF_COUNTRY_SINGLE_CONFIRMATION.format(ldmf_country=ldmf_country),
                next_expected_field=None,
                options=[ldmf_country],
            )


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if dates are already confirmed (BOTH start and end dates required)
        if confirmed_data.date_intervals:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if dates exist in aggregated data
        if aggregated_data.date_intervals:
            # If we have date intervals, ask for confirmation
            start_date, end_date = aggregated_data.date_intervals[0]
            if start_date or end_date:
                date_info = []
                if start_date:
                    date_info.append(f'Start: {start_date}')
                if end_date:
                    date_info.append(f'End: {end_date}')
                formatted_dates = ' | '.join(date_info)
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.PENDING_CONFIRMATION,
                    system_message=DATES_CONFIRMATION.format(dates=formatted_dates),
                    next_expected_field=None,
                    options=aggregated_data.date_intervals,
                )

        # No dates found - ask user to provide them
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=NEED_INFO_DATES,
            next_expected_field=RequiredField.ENGAGEMENT_DATES,
        )


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if objective_and_scope is already confirmed
        if confirmed_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if objective_and_scope is in aggregated data
        elif aggregated_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f'Found this objective and scope: {aggregated_data.objective_and_scope}. Can you confirm?',
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OBJECTIVE_SCOPE,
                next_expected_field=RequiredField.OBJECTIVE_SCOPE,
            )


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=OUTCOMES_AGGREGATED_QUESTION.format(outcomes=aggregated_data.outcomes),
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OUTCOMES,
                next_expected_field=None,
            )
